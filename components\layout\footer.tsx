"use client";

import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";
import { useAuthState } from "react-firebase-hooks/auth";
import { useDocument } from "react-firebase-hooks/firestore";
import { doc } from "firebase/firestore";
import { useRouter } from "next/navigation";
import { auth, db } from "@/lib/firebase/firebase";
import { signInWithGoogle, firebaseSignOut } from "@/lib/firebase/auth";

export default function Footer() {
  const [user, loading] = useAuthState(auth);
  const [mounted, setMounted] = useState(false);
  const [showBookingModal, setShowBookingModal] = useState(false);
  const router = useRouter();

  const [userDoc, userDocLoading] = useDocument(
    user ? doc(db, "users", user.uid) : null
  );

  useEffect(() => {
    setMounted(true);
  }, []);

  const dashboardLink = userDoc?.data()?.isAdmin ? "/admin" : "/time";
  const isAdmin = userDoc?.data()?.isAdmin || false;

  const handleSignOut = async () => {
    await firebaseSignOut();
    router.push("/");
  };

  return (
    <footer className="bg-[#00072D] text-white pt-12 pb-6 border-t border-[#001C55] font-sans">
      <div className="max-w-7xl mx-auto px-5">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="flex flex-col items-center">
            <h3 className="font-bold text-2xl mb-4 text-white font-sans">
              <span className="italic">Our Company</span>
            </h3>
            <p className="text-[#7CB9E8] text-sm font-sans">
              Our team of skilled cleaning technicians are providing exceptional commercial and residential cleaning services to the local area and islands. We are committed to reliability, professionalism, and an unwavering dedication to customer satisfaction.
            </p>
            <div className="flex space-x-4 mt-4">
              <a
                href="https://facebook.com"
                className="text-[#7CB9E8] hover:text-white transition-colors"
                aria-label="Facebook"
              >
                <svg
                  className="w-6 h-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    fillRule="evenodd"
                    d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                    clipRule="evenodd"
                  />
                </svg>
              </a>
              <a
                href="https://yelp.com"
                className="text-[#7CB9E8] hover:text-white transition-colors"
                aria-label="Yelp"
              >
                <svg
                  className="w-6 h-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987s11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.418-3.323c.928-.875 2.079-1.365 3.323-1.365s2.448.49 3.323 1.365c.928.875 1.418 2.026 1.418 3.323s-.49 2.448-1.418 3.244c-.875.807-2.026 1.297-3.323 1.297zm7.83-9.605c-.49 0-.928-.368-.928-.928 0-.49.438-.928.928-.928.49 0 .928.438.928.928 0 .56-.438.928-.928.928zm-7.83 1.297c1.051 0 1.911.86 1.911 1.911s-.86 1.911-1.911 1.911-1.911-.86-1.911-1.911.86-1.911 1.911-1.911z" />
                </svg>
              </a>

            </div>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white mb-3 font-sans">
              Operating Hours
            </h3>
            <ul className="text-[#B6D0E2] text-sm space-y-2 font-sans">
              <li>Mon - Fri: 7am - 9pm</li>
              <li>Saturday: 12pm - 5pm</li>
              <li>Sunday: 12pm - 5pm</li>
            </ul>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white mb-3 font-sans">
              Contact Information
            </h3>
            <ul className="text-[#B6D0E2] text-sm space-y-2 font-sans">
              <li>
                <a
                  href="tel:+19078211335"
                  className="hover:underline hover:text-white transition-colors"
                >
                  (*************
                </a>
              </li>
              <li>
                <a
                  href="mailto:<EMAIL>"
                  className="hover:underline hover:text-white transition-colors"
                >
                  <EMAIL>
                </a>
              </li>
              <li className="mt-4">
                <div className="text-[#B6D0E2] text-xs">
                  <p>Licensed, Bonded, & Insured</p>
                  <p>Alaska Business License #: 2164310</p>
                </div>
              </li>
            </ul>
          </div>
        </div>



        <div className="mt-8 text-[#7CB9E8] text-xs font-sans">
          <p>
            © 2024 Peak Services |{" "}
            <a
              href="/privacy"
              className="hover:underline hover:text-white transition-colors"
            >
              Privacy Policy
            </a>{" "}
            |{" "}
            <a
              href="/terms"
              className="hover:underline hover:text-white transition-colors"
            >
              Terms & Conditions
            </a>
          </p>
        </div>
      </div>

      
    </footer>
  );
}
