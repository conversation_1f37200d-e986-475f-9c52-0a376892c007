"use client";
import Image from "next/image";
import { useState } from "react";
import CleaningServiceRequest from "@/components/CleaningServiceRequest";

// Strongly-typed categories for cleaning services
type CategoryKey = "residential" | "commercial" | "specialized";

interface ServiceDetail {
    title: string;
    description: string;
    image: string;
    category: CategoryKey;
    features?: string[];
}

const servicesDetails: ServiceDetail[] = [
    // Residential Services
    {
        title: "Regular Residential Cleaning",
        description:
            "Our comprehensive residential cleaning service covers all the essentials to keep your home spotless. From dusting and vacuuming to bathroom and kitchen deep cleaning, we ensure every corner of your home is pristine.",
        image: "/residential couch cleaning.png",
        category: "residential",
        features: ["Weekly, bi-weekly, or monthly service", "All rooms cleaned thoroughly", "Eco-friendly options available", "Satisfaction guaranteed"]
    },
    {
        title: "Deep Cleaning Service",
        description:
            "Perfect for spring cleaning or when you need that extra attention to detail. Our deep cleaning service tackles areas often missed in regular cleaning routines.",
        image: "/residential bedroom cleaning.png",
        category: "residential",
        features: ["Detailed cleaning of all surfaces", "Inside appliances", "Baseboards and window sills", "Light fixtures and ceiling fans"]
    },
    {
        title: "Move-In/Move-Out Cleaning",
        description:
            "Starting fresh in a new home or preparing to leave? Our move-in/move-out cleaning ensures your space is perfectly clean for the transition.",
        image: "/residential couch cleaning 2.png",
        category: "residential",
        features: ["Complete property cleaning", "Inside cabinets and drawers", "Appliance cleaning", "Ready for occupancy"]
    },
    {
        title: "Vacation Rental Turnarounds",
        description:
            "Keep your vacation rental property guest-ready with our efficient turnaround cleaning service. We ensure your property is spotless between guests.",
        image: "/Bathroom floor cleaning.png",
        category: "residential",
        features: ["Quick turnaround times", "Guest-ready standards", "Linen and towel service available", "Property inspection included"]
    },
    {
        title: "One-Time Deep Clean",
        description:
            "Perfect for special occasions, seasonal cleaning, or when you need that extra sparkle. Our one-time deep cleaning service gives your home the thorough attention it deserves.",
        image: "/residential bedroom cleaning.png",
        category: "residential",
        features: ["Comprehensive deep cleaning", "No recurring commitment", "Perfect for special events", "Move-in ready cleaning"]
    },
    // Commercial Services
    {
        title: "Office Cleaning",
        description:
            "Professional office cleaning services to maintain a clean, healthy, and productive work environment for your employees and clients.",
        image: "/office cleaning.png",
        category: "commercial",
        features: ["Daily, weekly, or monthly service", "Restroom sanitization", "Trash removal", "Floor care and maintenance"]
    },
    {
        title: "Commercial Floor Cleaning",
        description:
            "Specialized floor cleaning and maintenance for all types of commercial flooring including tile, hardwood, carpet, and vinyl.",
        image: "/commercial floor cleaning.png",
        category: "commercial",
        features: ["Floor stripping and waxing", "Carpet deep cleaning", "Tile and grout cleaning", "Hardwood floor care"]
    },
    {
        title: "Commercial Window Cleaning",
        description:
            "Professional window cleaning services for commercial buildings, ensuring crystal-clear windows that enhance your business's appearance.",
        image: "/commercial window cleaning.png",
        category: "commercial",
        features: ["Interior and exterior cleaning", "High-rise window access", "Screen cleaning", "Regular maintenance schedules"]
    },
    {
        title: "Commercial Bathroom Janitorial",
        description:
            "Complete commercial restroom cleaning and sanitization services to maintain hygiene standards and create a pleasant experience for users.",
        image: "/commercial bathroom janitorial.png",
        category: "commercial",
        features: ["Deep sanitization", "Supply restocking", "Fixture cleaning", "Floor and tile maintenance"]
    },
    {
        title: "Retail Store Cleaning",
        description:
            "Specialized cleaning services for retail environments, ensuring your store always looks its best for customers while maintaining a professional appearance.",
        image: "/office cleaning.png",
        category: "commercial",
        features: ["After-hours cleaning", "Display area maintenance", "Customer area focus", "Flexible scheduling"]
    },
    {
        title: "Medical Facility Cleaning",
        description:
            "Specialized cleaning and sanitization for medical offices, clinics, and healthcare facilities with strict hygiene and safety protocols.",
        image: "/commercial bathroom janitorial.png",
        category: "commercial",
        features: ["Medical-grade sanitization", "HIPAA compliance", "Specialized equipment cleaning", "Infection control protocols"]
    },
    // Specialized Services
    {
        title: "Carpet Shampooing",
        description:
            "Professional carpet cleaning and shampooing services to remove deep-seated dirt, stains, and odors, extending the life of your carpets.",
        image: "/carpet cleaning.png",
        category: "specialized",
        features: ["Deep extraction cleaning", "Stain removal", "Odor elimination", "Fast drying techniques"]
    },
    {
        title: "Window Washing",
        description:
            "Professional window washing services for residential and commercial properties, ensuring streak-free, crystal-clear windows.",
        image: "/peak services window cleaning.png",
        category: "specialized",
        features: ["Interior and exterior washing", "Screen cleaning", "Sill and frame cleaning", "Eco-friendly solutions"]
    },
    {
        title: "Dryer Vent Cleaning",
        description:
            "Essential dryer vent cleaning service to improve efficiency, reduce energy costs, and prevent fire hazards in your home or business.",
        image: "/peak services employee putting on hardhat.png",
        category: "specialized",
        features: ["Complete vent system cleaning", "Lint removal", "Safety inspection", "Improved dryer efficiency"]
    },
    {
        title: "Pressure Washing",
        description:
            "High-pressure cleaning services for exterior surfaces including driveways, sidewalks, building exteriors, and outdoor areas.",
        image: "/commercial floor cleaning 2.png",
        category: "specialized",
        features: ["Driveway and sidewalk cleaning", "Building exterior washing", "Deck and patio cleaning", "Graffiti removal"]
    },
    {
        title: "Post-Construction Cleanup",
        description:
            "Comprehensive cleanup services after construction or renovation projects, removing debris and preparing spaces for occupancy.",
        image: "/floor cleaning 2.png",
        category: "specialized",
        features: ["Debris removal", "Dust and residue cleaning", "Window and fixture cleaning", "Final inspection ready"]
    },
    {
        title: "Industrial Area Cleanup",
        description:
            "Specialized cleaning services for industrial facilities, warehouses, and manufacturing spaces with heavy-duty cleaning requirements.",
        image: "/floor cleaning.png",
        category: "specialized",
        features: ["Heavy-duty equipment cleaning", "Safety compliance", "Hazardous material handling", "Large area coverage"]
    },
    {
        title: "Grounds Keeping",
        description:
            "Comprehensive grounds maintenance including weeding, trash pickup, pruning, fertilizing, and general outdoor area maintenance.",
        image: "/floor cleaning.png",
        category: "specialized",
        features: ["Weeding and landscaping", "Trash and debris removal", "Pruning and trimming", "Fertilizing and lawn care"]
    }
];

const categoryOrder: CategoryKey[] = ["residential", "commercial", "specialized"];

const categoryTitles = {
    residential: "Residential Cleaning Services",
    commercial: "Commercial Cleaning Services", 
    specialized: "Specialized Cleaning Services"
};

const ServicesPage = () => {
    const [showBookingModal, setShowBookingModal] = useState(false);

    return (
        <main className="flex flex-col items-center min-h-screen space-y-8 font-sans">
            {/* Service Request Modal */}
            {showBookingModal && (
                <CleaningServiceRequest onClose={() => setShowBookingModal(false)} />
            )}

            {/* Hero Section */}
            <section className="w-full py-5 md:pt-16 md:pb-4">
                <div className="max-w-6xl mx-auto px-2 text-center">
                    <h1 className="mb-2 text-6xl md:text-6xl font-bold text-white tracking-tight">
                        Our Cleaning Services
                    </h1>
                    <p className="text-lg text-white/90 max-w-3xl mx-auto mb-8">
                        Peak Services AK offers comprehensive cleaning solutions for residential, commercial, and specialized needs throughout Ketchikan Alaska and all of Southeast Alaska.
                    </p>
                    
                    <button
                        onClick={() => setShowBookingModal(true)}
                        className="group relative bg-pink-500 text-white px-8 py-4 rounded-lg font-semibold hover:bg-pink-600 transition-all transform hover:scale-105 hover:translate-y-[-5px] hover:shadow-lg hover:shadow-pink-500 shiny-button border border-white"
                    >
                        <span className="relative z-10 italic font-bold text-xl tracking-tighter">
                            REQUEST A QUOTE
                        </span>
                    </button>
                </div>
            </section>

            {/* Service Area Section */}
            <section className="w-full py-16 px-2 mb-8">
                <div className="max-w-4xl mx-auto text-center">
                    <h2 className="text-4xl md:text-5xl font-bold text-white mb-8 tracking-tighter">
                        Service Areas
                    </h2>
                    <div className="bg-[rgba(15,55,255,0.9)] backdrop-blur-sm rounded-xl p-8 border border-white/20">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
                            <div className="text-left">
                                <h3 className="text-2xl font-bold text-white mb-4">We Proudly Serve:</h3>
                                <ul className="space-y-2 text-gray-300">
                                    <li className="flex items-center">
                                        <span className="text-pink-400 mr-3">📍</span>
                                        <span>Ketchikan, Alaska</span>
                                    </li>
                                    <li className="flex items-center">
                                        <span className="text-pink-400 mr-3">📍</span>
                                        <span>All of Southeast Alaska</span>
                                    </li>
                                    <li className="flex items-center">
                                        <span className="text-pink-400 mr-3">📍</span>
                                        <span>Surrounding Islands</span>
                                    </li>
                                    <li className="flex items-center">
                                        <span className="text-pink-400 mr-3">📍</span>
                                        <span>Remote Locations (Special Arrangements)</span>
                                    </li>
                                </ul>
                                <div className="mt-6 p-4 bg-green-500/20 rounded-lg border border-green-400/30">
                                    <p className="text-green-300 text-sm">
                                        <strong>✅ Licensed, Bonded & Insured</strong><br />
                                        Alaska Business License #: 2164310
                                    </p>
                                </div>
                            </div>
                            <div className="relative">
                                <div className="bg-white/10 rounded-lg p-6 border border-white/20">
                                    <h4 className="text-xl font-bold text-white mb-4">🗺️ Coverage Map</h4>
                                    <div className="text-gray-300 space-y-2">
                                        <p><strong className="text-white">Primary Service Area:</strong> Ketchikan & Immediate Vicinity</p>
                                        <p><strong className="text-white">Extended Service:</strong> Southeast Alaska Communities</p>
                                        <p><strong className="text-white">Travel Services:</strong> Remote locations available with advance notice</p>
                                    </div>
                                    <div className="mt-4 p-3 bg-blue-500/20 rounded border border-blue-400/30">
                                        <p className="text-blue-300 text-sm">
                                            Contact us to confirm service availability in your specific location!
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section className="w-full max-w-6xl mx-auto px-2 text-center relative">
                {/* Three columns: specific category grouping */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {categoryOrder.map((cat) => (
                        <div
                            key={cat}
                            id={cat}
                            className="space-y-6 p-6 rounded-xl scroll-mt-20 bg-slate-900/30"
                        >
                            <h2 className="text-3xl font-bold text-white mb-6 tracking-tight">
                                {categoryTitles[cat]}
                            </h2>
                            {servicesDetails
                                .filter((s) => s.category === cat)
                                .map((service) => (
                                    <div
                                        key={service.title}
                                        className="group overflow-hidden relative rounded-xl bg-[rgba(15,55,255,0.9)] backdrop-blur-sm border border-white/20"
                                    >
                                        <div className="relative h-48 overflow-hidden">
                                            <Image
                                                src={service.image}
                                                alt={service.title}
                                                fill
                                                className="object-cover transition-transform duration-300 group-hover:scale-110"
                                            />
                                            <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                                        </div>
                                        <div className="p-6 text-left">
                                            <h3 className="text-xl font-bold text-white mb-3 tracking-tight">
                                                {service.title}
                                            </h3>
                                            <p className="text-gray-300 mb-4 text-sm leading-relaxed">
                                                {service.description}
                                            </p>
                                            {service.features && (
                                                <ul className="text-gray-400 text-xs space-y-1">
                                                    {service.features.map((feature, idx) => (
                                                        <li key={idx} className="flex items-center">
                                                            <span className="text-pink-400 mr-2">•</span>
                                                            {feature}
                                                        </li>
                                                    ))}
                                                </ul>
                                            )}
                                        </div>
                                    </div>
                                ))}
                        </div>
                    ))}
                </div>
            </section>

            {/* Pricing Information Section */}
            <section className="w-full py-16 px-2 mb-8">
                <div className="max-w-6xl mx-auto">
                    <h2 className="text-4xl md:text-5xl font-bold text-white text-center mb-12 tracking-tighter">
                        Transparent Pricing
                    </h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        <div className="bg-[rgba(15,55,255,0.9)] backdrop-blur-sm rounded-xl p-8 border border-white/20">
                            <h3 className="text-2xl font-bold text-white mb-4">Residential Cleaning</h3>
                            <div className="space-y-3 text-gray-300">
                                <p><strong className="text-white">Regular Cleaning</strong> Custom quote </p>
                                <p><strong className="text-white">Deep Cleaning</strong> Custom quote</p>
                                <p><strong className="text-white">Move-in/out</strong> Custom quote</p>
                                <p className="text-sm text-gray-400 mt-4">*2-hour minimum for all residential services</p>
                            </div>
                        </div>

                        <div className="bg-[rgba(15,55,255,0.9)] backdrop-blur-sm rounded-xl p-8 border border-white/20">
                            <h3 className="text-2xl font-bold text-white mb-4">Commercial Cleaning</h3>
                            <div className="space-y-3 text-gray-300">
                                <p><strong className="text-white">Office Cleaning:</strong> Custom contracts</p>
                                <p><strong className="text-white">Floor Care:</strong> Quote after walkthrough</p>
                                <p><strong className="text-white">Window Cleaning:</strong> Per window pricing</p>
                                <p className="text-sm text-gray-400 mt-4">*Pricing based on square footage and frequency</p>
                            </div>
                        </div>

                        <div className="bg-[rgba(15,55,255,0.9)] backdrop-blur-sm rounded-xl p-8 border border-white/20">
                            <h3 className="text-2xl font-bold text-white mb-4">Specialized Services</h3>
                            <div className="space-y-3 text-gray-300">
                                <p><strong className="text-white">Carpet Cleaning:</strong> $130 minimum</p>
                                <p><strong className="text-white">Pressure Washing:</strong> Custom quote</p>
                                <p><strong className="text-white">Post-Construction:</strong> Project-based</p>
                                <p className="text-sm text-gray-400 mt-4">*All specialized services include free estimates</p>
                            </div>
                        </div>
                    </div>

                    <div className="mt-12 bg-gradient-to-r from-green-500 to-green-600 rounded-xl p-8 text-center">
                        <h3 className="text-2xl font-bold text-white mb-4">💰 Recurring Service Discounts</h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-white">
                            <div>
                                <p className="font-bold text-lg">Weekly Service</p>
                                <p className="text-green-100">Save 15%</p>
                            </div>
                            <div>
                                <p className="font-bold text-lg">Bi-Weekly Service</p>
                                <p className="text-green-100">Save 10%</p>
                            </div>
                            <div>
                                <p className="font-bold text-lg">Monthly Service</p>
                                <p className="text-green-100">Save 5%</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Contact CTA */}
            <section className="w-full py-16 px-2 mb-8">
                <div className="max-w-4xl mx-auto px-2">
                    <div className="bg-gradient-to-br from-blue-500 to-blue-600 text-white py-12 px-8 rounded-lg text-center">
                        <h2 className="text-4xl font-bold mb-6 tracking-tighter">
                            Ready to Get Started?
                        </h2>
                        <p className="text-xl mb-8 max-w-2xl mx-auto text-white/90">
                            Contact Peak Services AK today for professional cleaning services in Ketchikan Alaska and all of Southeast Alaska.
                        </p>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                            <a
                                href="tel:+19078211335"
                                className="group relative bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-all transform hover:scale-105 hover:-translate-y-[2px] hover:shadow-lg"
                            >
                                <span className="relative z-10 font-bold text-lg tracking-tighter">
                                    📞 (*************
                                </span>
                            </a>
                            <a
                                href="mailto:<EMAIL>"
                                className="group relative bg-pink-500 text-white px-8 py-4 rounded-lg font-semibold hover:bg-pink-600 transition-all transform hover:scale-105 hover:-translate-y-[2px] hover:shadow-lg"
                            >
                                <span className="relative z-10 font-bold text-lg tracking-tighter">
                                    ✉️ Email Us
                                </span>
                            </a>
                            <button
                                onClick={() => setShowBookingModal(true)}
                                className="group relative bg-green-500 text-white px-8 py-4 rounded-lg font-semibold hover:bg-green-600 transition-all transform hover:scale-105 hover:-translate-y-[2px] hover:shadow-lg"
                            >
                                <span className="relative z-10 font-bold text-lg tracking-tighter">
                                    📋 Request Service
                                </span>
                            </button>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    );
};

export default ServicesPage;
