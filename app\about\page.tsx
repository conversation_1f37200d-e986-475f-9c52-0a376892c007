"use client";

import { helvetica } from "../fonts";
import cx from "classnames";
import { useState } from "react";
import Image from "next/image";
import CleaningServiceRequest from "@/components/CleaningServiceRequest";

export default function About() {
    const [showBookingModal, setShowBookingModal] = useState(false);

    return (
        <div
            className={cx(
                helvetica.variable,
                "pt-20 flex flex-col items-center min-h-screen space-y-8 p-4 sm:p-8 bg-transparent text-gray-100 font-sans"
            )}
        >
            {/* Service Request Modal */}
            {showBookingModal && (
                <CleaningServiceRequest onClose={() => setShowBookingModal(false)} />
            )}

            {/* Header */}
            <div className="text-center py-10 px-6">
                <h1 className="text-4xl md:text-6xl font-bold text-white tracking-tighter">
                    About Peak Services AK
                </h1>
                <h3 className="mt-6 text-lg text-white/90 md:text-xl font-medium">
                    Your trusted cleaning partner in Alaska
                </h3>
            </div>

            {/* Main Content */}
            <div className="w-full max-w-6xl mx-auto mt-10 space-y-12 bg-[rgba(15,55,255,0.9)] backdrop-blur-sm rounded-xl p-8">
                
                {/* Our Story Section */}
                <section className="space-y-6">
                    <h2 className="text-3xl font-bold text-white mb-6">Our Story</h2>
                    
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                        <div className="space-y-4">
                            <p className="text-gray-300 leading-relaxed">
                                Hello! I'm <strong className="text-white">Megan Olmstead</strong>, founder and owner of Peak Services AK. What started as a simple desire to help busy families and local businesses maintain clean, healthy spaces has grown into Alaska's trusted cleaning partner.
                            </p>
                            <p className="text-gray-300 leading-relaxed">
                                Born and raised in Alaska, I understand the unique challenges our climate and lifestyle present. I also know the value of hard work, reliability, and treating every customer like family. In 2022, I founded Peak Services with a simple mission: to give Alaskans their time back while ensuring they always come home to—or work in—a spotless space.
                            </p>
                        </div>
                        
                        <div className="flex justify-center">
                            <div className="relative w-80 h-80 rounded-xl overflow-hidden border-4 border-white/20">
                                <Image
                                    src="/megan owner of peak services.png"
                                    alt="Megan Olmstead - Owner of Peak Services AK"
                                    fill
                                    className="object-cover"
                                />
                            </div>
                        </div>
                    </div>
                </section>

                {/* Why We're Different Section */}
                <section className="space-y-6">
                    <h2 className="text-3xl font-bold text-white mb-6">Why We're Different</h2>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="bg-blue-600/30 p-6 rounded-lg border border-white/20">
                            <h3 className="text-xl font-semibold text-white mb-3">Local Expertise, Personal Service</h3>
                            <p className="text-gray-300">
                                As lifelong Alaskans, we understand our community's needs. We're not a franchise or national chain—we're your neighbors who take genuine pride in serving our local community. Your satisfaction directly reflects on our reputation, which means everything to us.
                            </p>
                        </div>
                        
                        <div className="bg-blue-600/30 p-6 rounded-lg border border-white/20">
                            <h3 className="text-xl font-semibold text-white mb-3">Comprehensive Solutions</h3>
                            <p className="text-gray-300">
                                Whether you need weekly house cleaning, commercial janitorial services, or specialized deep cleaning, we have the experience and equipment to handle it all. From cozy homes to large commercial facilities, no job is too big or small.
                            </p>
                        </div>
                        
                        <div className="bg-blue-600/30 p-6 rounded-lg border border-white/20">
                            <h3 className="text-xl font-semibold text-white mb-3">Flexible & Budget-Friendly</h3>
                            <p className="text-gray-300">
                                We believe everyone deserves a clean space, regardless of budget. That's why we work with you to create customized cleaning plans that fit your needs and financial situation. Quality cleaning shouldn't be a luxury—it should be accessible.
                            </p>
                        </div>
                        
                        <div className="bg-blue-600/30 p-6 rounded-lg border border-white/20">
                            <h3 className="text-xl font-semibold text-white mb-3">Trained, Trusted Team</h3>
                            <p className="text-gray-300">
                                Our professional cleaning technicians are thoroughly trained, insured, and bonded. We invest in ongoing education to ensure we're using the latest techniques and safest products for your family or business.
                            </p>
                        </div>
                    </div>
                </section>

                {/* Mission Section */}
                <section className="bg-gradient-to-r from-pink-500/20 to-blue-500/20 p-8 rounded-xl border border-white/20">
                    <h2 className="text-3xl font-bold text-white mb-6 text-center">Our Mission</h2>
                    <div className="text-center">
                        <p className="text-xl text-white font-semibold mb-4">
                            "We believe everyone deserves to work in a clean office AND come home to a clean home after a long day's work."
                        </p>
                        <p className="text-gray-300 leading-relaxed max-w-3xl mx-auto">
                            At Peak Services AK, we're committed to working as effectively and efficiently as possible while maintaining the highest standards of cleanliness. Our goal is simple: to exceed your expectations every single time.
                        </p>
                    </div>
                </section>

                {/* Services Section */}
                <section className="space-y-6">
                    <h2 className="text-3xl font-bold text-white mb-6">Services We're Proud to Offer</h2>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="bg-blue-600/30 p-6 rounded-lg border border-white/20">
                            <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
                                🏠 Residential Cleaning
                            </h3>
                            <ul className="text-gray-300 space-y-2 text-sm">
                                <li>• Regular housekeeping (weekly, bi-weekly, monthly)</li>
                                <li>• Deep cleaning and move-in/move-out services</li>
                                <li>• Vacation rental turnarounds</li>
                                <li>• Post-construction cleanup</li>
                            </ul>
                        </div>
                        
                        <div className="bg-blue-600/30 p-6 rounded-lg border border-white/20">
                            <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
                                🏢 Commercial Services
                            </h3>
                            <ul className="text-gray-300 space-y-2 text-sm">
                                <li>• Office cleaning and janitorial services</li>
                                <li>• Retail and healthcare facility cleaning</li>
                                <li>• Floor care (stripping, waxing, carpet cleaning)</li>
                                <li>• Customized maintenance contracts</li>
                            </ul>
                        </div>
                        
                        <div className="bg-blue-600/30 p-6 rounded-lg border border-white/20">
                            <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
                                ✨ Specialized Services
                            </h3>
                            <ul className="text-gray-300 space-y-2 text-sm">
                                <li>• Window washing</li>
                                <li>• Carpet shampooing</li>
                                <li>• Pressure washing</li>
                                <li>• Appliance and upholstery cleaning</li>
                            </ul>
                        </div>
                    </div>
                </section>

                {/* Our Commitment Section */}
                <section className="space-y-6">
                    <h2 className="text-3xl font-bold text-white mb-6">Our Commitment to You</h2>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div className="flex items-center space-x-3 bg-green-600/20 p-4 rounded-lg border border-green-400/30">
                            <span className="text-green-400 text-xl">✓</span>
                            <div>
                                <h4 className="text-white font-semibold">Satisfaction Guaranteed</h4>
                                <p className="text-gray-300 text-sm">We're not happy unless you're completely satisfied</p>
                            </div>
                        </div>

                        <div className="flex items-center space-x-3 bg-green-600/20 p-4 rounded-lg border border-green-400/30">
                            <span className="text-green-400 text-xl">✓</span>
                            <div>
                                <h4 className="text-white font-semibold">Reliable Service</h4>
                                <p className="text-gray-300 text-sm">Consistent, dependable cleaning you can count on</p>
                            </div>
                        </div>

                        <div className="flex items-center space-x-3 bg-green-600/20 p-4 rounded-lg border border-green-400/30">
                            <span className="text-green-400 text-xl">✓</span>
                            <div>
                                <h4 className="text-white font-semibold">Transparent Pricing</h4>
                                <p className="text-gray-300 text-sm">No hidden fees or surprise charges</p>
                            </div>
                        </div>

                        <div className="flex items-center space-x-3 bg-green-600/20 p-4 rounded-lg border border-green-400/30">
                            <span className="text-green-400 text-xl">✓</span>
                            <div>
                                <h4 className="text-white font-semibold">Eco-Friendly Options</h4>
                                <p className="text-gray-300 text-sm">Safe products for your family and the environment</p>
                            </div>
                        </div>

                        <div className="flex items-center space-x-3 bg-green-600/20 p-4 rounded-lg border border-green-400/30">
                            <span className="text-green-400 text-xl">✓</span>
                            <div>
                                <h4 className="text-white font-semibold">Fully Insured & Bonded</h4>
                                <p className="text-gray-300 text-sm">Complete peace of mind with every service</p>
                            </div>
                        </div>
                    </div>
                </section>

                {/* By the Numbers Section */}
                <section className="bg-gradient-to-r from-blue-600/20 to-purple-600/20 p-8 rounded-xl border border-white/20">
                    <h2 className="text-3xl font-bold text-white mb-6 text-center">By the Numbers</h2>
                    <div className="text-center space-y-4">
                        <p className="text-gray-300 leading-relaxed">
                            Since opening our doors in 2022, we've been honored to serve hundreds of satisfied customers across Alaska. Our small but dedicated team of five professional cleaners has transformed countless homes and businesses, building lasting relationships one spotless space at a time.
                        </p>
                        <p className="text-gray-300 leading-relaxed">
                            But the numbers that matter most to us aren't the ones we can count—they're the smiles on our customers' faces when they walk into their perfectly clean space, and the trust they show by referring us to their friends and family.
                        </p>
                    </div>
                </section>

                {/* Customer Testimonials Section */}
                <section className="space-y-6">
                    <h2 className="text-3xl font-bold text-white mb-6">What Our Customers Say</h2>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="bg-blue-600/30 p-6 rounded-lg border border-white/20">
                            <div className="flex items-start space-x-4">
                                <div className="text-yellow-400 text-2xl">⭐⭐⭐⭐⭐</div>
                            </div>
                            <p className="text-gray-300 italic mt-4 mb-4">
                                "Megan and her team are absolutely fantastic. They consistently go above and beyond, and I never have to worry about the quality of their work. It's such a relief to come home to a perfectly clean house after a long day!"
                            </p>
                            <p className="text-white font-semibold">— Sarah M., Residential Customer</p>
                        </div>

                        <div className="bg-blue-600/30 p-6 rounded-lg border border-white/20">
                            <div className="flex items-start space-x-4">
                                <div className="text-yellow-400 text-2xl">⭐⭐⭐⭐⭐</div>
                            </div>
                            <p className="text-gray-300 italic mt-4 mb-4">
                                "Peak Services has been cleaning our office for over a year now. Professional, reliable, and always willing to accommodate our changing needs. Highly recommended!"
                            </p>
                            <p className="text-white font-semibold">— Tom R., Commercial Client</p>
                        </div>
                    </div>
                </section>

                {/* CTA Section */}
                <section className="bg-gradient-to-r from-pink-500/20 to-blue-500/20 p-8 rounded-xl border border-white/20 text-center">
                    <h2 className="text-3xl font-bold text-white mb-4">Ready to Experience the Peak Services Difference?</h2>
                    <p className="text-gray-300 mb-6 max-w-3xl mx-auto">
                        We'd love to show you what personalized, professional cleaning service looks like. Whether you need regular housekeeping, a one-time deep clean, or comprehensive commercial services, we're here to help.
                    </p>

                    <div className="space-y-4">
                        <h3 className="text-xl font-semibold text-white">Get your free estimate today:</h3>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                            <button
                                onClick={() => setShowBookingModal(true)}
                                className="bg-pink-500 text-white px-8 py-4 rounded-lg font-semibold hover:bg-pink-600 transition-all transform hover:scale-105 hover:-translate-y-[2px] hover:shadow-lg"
                            >
                                📝 Fill out our online request form
                            </button>
                            <a
                                href="tel:+19078211335"
                                className="bg-blue-500 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-600 transition-all transform hover:scale-105 hover:-translate-y-[2px] hover:shadow-lg"
                            >
                                📞 Call us for immediate assistance
                            </a>
                            <a
                                href="mailto:<EMAIL>"
                                className="bg-green-500 text-white px-8 py-4 rounded-lg font-semibold hover:bg-green-600 transition-all transform hover:scale-105 hover:-translate-y-[2px] hover:shadow-lg"
                            >
                                ✉️ Email us with your questions
                            </a>
                        </div>
                    </div>

                    <div className="mt-8 p-6 bg-blue-600/30 rounded-lg border border-white/20">
                        <p className="text-white font-semibold text-lg italic">
                            "At Peak Services AK, we don't just clean—we care. Let us take care of the cleaning so you can focus on what matters most to you."
                        </p>
                        <p className="text-gray-300 mt-2 font-medium">
                            Peak Services AK - Committed to Keeping Your Home and Business Squeaky Clean
                        </p>
                    </div>
                </section>
            </div>
        </div>
    );
}
