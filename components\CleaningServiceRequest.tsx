"use client";
import React, { useState } from "react";

interface CleaningServiceFormData {
  // Contact Information
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  
  // Property Address
  streetAddress: string;
  apartment: string;
  city: string;
  state: string;
  zipCode: string;
  
  // Billing Address
  billingDifferent: boolean;
  billingStreetAddress: string;
  billingApartment: string;
  billingCity: string;
  billingState: string;
  billingZipCode: string;
  
  // Property Details
  propertyType: string;
  bedrooms: string;
  bathrooms: string;
  howHeard: string;
  
  // Special Requirements
  roomsToSkip: string;
  allergies: string;
  
  // Service Preferences
  cleaningPriorities: string;
  
  // Additional Services
  additionalServices: string[];
  
  // Service Frequency
  frequency: string;
  
  // Budget
  budget: string;
  
  // Agreement
  agreedToTerms: boolean;
}

interface CleaningServiceRequestProps {
  onClose: () => void;
}

const CleaningServiceRequest: React.FC<CleaningServiceRequestProps> = ({ onClose }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<CleaningServiceFormData>({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    streetAddress: "",
    apartment: "",
    city: "",
    state: "",
    zipCode: "",
    billingDifferent: false,
    billingStreetAddress: "",
    billingApartment: "",
    billingCity: "",
    billingState: "",
    billingZipCode: "",
    propertyType: "",
    bedrooms: "",
    bathrooms: "",
    howHeard: "",
    roomsToSkip: "",
    allergies: "",
    cleaningPriorities: "",
    additionalServices: [],
    frequency: "",
    budget: "",
    agreedToTerms: false,
  });

  const propertyTypes = [
    "Residential Home",
    "Apartment/Condo", 
    "Office",
    "Commercial Space",
    "Other"
  ];

  const bedroomOptions = [
    "Not Applicable",
    "1", "2", "3", "4", "5", "6+"
  ];

  const bathroomOptions = [
    "1", "1.5", "2", "2.5", "3", "3.5", "4+"
  ];

  const howHeardOptions = [
    "Google Search",
    "Facebook",
    "Friend/Family Referral", 
    "Yelp",
    "Other"
  ];

  const additionalServiceOptions = [
    "Window Washing",
    "Carpet Shampooing",
    "Appliance Cleaning",
    "Upholstery Cleaning",
    "Baseboard Cleaning",
    "Washing Dirty Dishes",
    "Wall Cleaning",
    "Dishwasher Cleaning",
    "Blind Cleaning",
    "Oven Cleaning",
    "Steam Cleaning",
    "Dryer Vent Cleaning",
    "Pressure Washing",
    "Commercial Floor Services (strip/wax/burnish, etc.)",
    "Post Construction Clean Up"
  ];

  const frequencyOptions = [
    "One Time",
    "Weekly",
    "Bi-weekly",
    "Tri-weekly",
    "Daily (Commercial)"
  ];

  const handleInputChange = (field: keyof CleaningServiceFormData, value: string | boolean | string[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleAdditionalServiceToggle = (service: string) => {
    setFormData(prev => ({
      ...prev,
      additionalServices: prev.additionalServices.includes(service)
        ? prev.additionalServices.filter(s => s !== service)
        : [...prev.additionalServices, service]
    }));
  };

  const isStep1Valid = () => {
    return formData.firstName.trim() !== "" &&
           formData.lastName.trim() !== "" &&
           formData.email.trim() !== "" &&
           formData.phone.trim() !== "";
  };

  const isStep2Valid = () => {
    return formData.streetAddress.trim() !== "" &&
           formData.city.trim() !== "" &&
           formData.state.trim() !== "" &&
           formData.zipCode.trim() !== "";
  };

  const isStep3Valid = () => {
    return formData.propertyType !== "" &&
           formData.bathrooms !== "" &&
           formData.howHeard !== "";
  };

  const isStep4Valid = () => {
    return formData.cleaningPriorities.trim() !== "";
  };

  const isStep5Valid = () => {
    return formData.frequency !== "";
  };

  const isStep6Valid = () => {
    return formData.agreedToTerms;
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    setCurrentStep(7); // Show processing step
    
    try {
      // Here you would typically send the data to your backend
      const response = await fetch('/api/cleaning-request', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        setCurrentStep(8); // Show success step
        setTimeout(() => {
          onClose();
        }, 3000);
      } else {
        throw new Error('Failed to submit request');
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      alert('Failed to submit request. Please try again.');
      setCurrentStep(6); // Go back to final step
    } finally {
      setIsSubmitting(false);
    }
  };

  const nextStep = () => {
    if (currentStep < 6) {
      setCurrentStep(currentStep + 1);
    } else {
      handleSubmit();
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const canProceed = () => {
    switch (currentStep) {
      case 1: return isStep1Valid();
      case 2: return isStep2Valid();
      case 3: return isStep3Valid();
      case 4: return isStep4Valid();
      case 5: return isStep5Valid();
      case 6: return isStep6Valid();
      default: return false;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="flex flex-col w-full h-[95vh] bg-white shadow-lg rounded-lg border border-blue-100 backdrop-blur-sm mx-4 relative z-50 max-w-4xl">
        
        {/* Processing Step */}
        {currentStep === 7 && (
          <div className="absolute inset-0 z-10 flex items-center justify-center bg-gradient-to-r from-blue-400 via-blue-500 to-blue-600 animate-gradient-x rounded-lg">
            <p className="text-white text-lg font-semibold">Processing your request...</p>
          </div>
        )}

        {/* Success Step */}
        {currentStep === 8 && (
          <div className="absolute inset-0 z-10 flex flex-col items-center justify-center bg-gradient-to-r from-green-400 via-green-500 to-green-600 rounded-lg">
            <div className="text-white text-center">
              <div className="text-6xl mb-4">✓</div>
              <h2 className="text-2xl font-bold mb-2">Request Submitted!</h2>
              <p className="text-lg">We'll contact you within 24 hours with your free quote!</p>
            </div>
          </div>
        )}

        {/* Header */}
        <div className="flex justify-between items-center px-6 py-4 border-b border-blue-100">
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-blue-900">Peak Services AK - Cleaning Service Request Form</h1>
            <div className="w-full h-2 bg-blue-100 rounded-full mt-2">
              <div
                className="h-2 bg-blue-600 rounded-full transition-all duration-500"
                style={{ width: `${(currentStep / 6) * 100}%` }}
              />
            </div>
          </div>
          <button
            onClick={onClose}
            className="ml-4 px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg font-medium transition-colors"
            disabled={isSubmitting}
          >
            Cancel
          </button>
        </div>

        {/* Content Area */}
        <div className="flex-1 overflow-y-auto py-6 px-6">

          {/* Step 1: Contact Information */}
          {currentStep === 1 && (
            <div className="space-y-6">
              <h2 className="text-xl font-bold text-blue-900 mb-4">Contact Information</h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-blue-900 mb-2">
                    First Name *
                  </label>
                  <input
                    type="text"
                    value={formData.firstName}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                    className="w-full p-3 border border-blue-300 rounded-lg bg-white text-blue-900 placeholder-blue-700 focus:border-blue-600 focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter your first name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-blue-900 mb-2">
                    Last Name *
                  </label>
                  <input
                    type="text"
                    value={formData.lastName}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                    className="w-full p-3 border border-blue-300 rounded-lg bg-white text-blue-900 placeholder-blue-700 focus:border-blue-600 focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter your last name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-blue-900 mb-2">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className="w-full p-3 border border-blue-300 rounded-lg bg-white text-blue-900 placeholder-blue-700 focus:border-blue-600 focus:ring-2 focus:ring-blue-500"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-blue-900 mb-2">
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    className="w-full p-3 border border-blue-300 rounded-lg bg-white text-blue-900 placeholder-blue-700 focus:border-blue-600 focus:ring-2 focus:ring-blue-500"
                    placeholder="(*************"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Step 2: Property Address */}
          {currentStep === 2 && (
            <div className="space-y-6">
              <h2 className="text-xl font-bold text-blue-900 mb-4">Property Address</h2>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-blue-900 mb-2">
                    Street Address *
                  </label>
                  <input
                    type="text"
                    value={formData.streetAddress}
                    onChange={(e) => handleInputChange('streetAddress', e.target.value)}
                    className="w-full p-3 border border-blue-300 rounded-lg bg-white text-blue-900 placeholder-blue-700 focus:border-blue-600 focus:ring-2 focus:ring-blue-500"
                    placeholder="123 Main Street"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-blue-900 mb-2">
                    Apartment, suite, etc. (optional)
                  </label>
                  <input
                    type="text"
                    value={formData.apartment}
                    onChange={(e) => handleInputChange('apartment', e.target.value)}
                    className="w-full p-3 border border-blue-300 rounded-lg bg-white text-blue-900 placeholder-blue-700 focus:border-blue-600 focus:ring-2 focus:ring-blue-500"
                    placeholder="Apt 2B, Suite 100, etc."
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-blue-900 mb-2">
                      City *
                    </label>
                    <input
                      type="text"
                      value={formData.city}
                      onChange={(e) => handleInputChange('city', e.target.value)}
                      className="w-full p-3 border border-blue-300 rounded-lg bg-white text-blue-900 placeholder-blue-700 focus:border-blue-600 focus:ring-2 focus:ring-blue-500"
                      placeholder="Ketchikan"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-blue-900 mb-2">
                      State *
                    </label>
                    <input
                      type="text"
                      value={formData.state}
                      onChange={(e) => handleInputChange('state', e.target.value)}
                      className="w-full p-3 border border-blue-300 rounded-lg bg-white text-blue-900 placeholder-blue-700 focus:border-blue-600 focus:ring-2 focus:ring-blue-500"
                      placeholder="AK"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-blue-900 mb-2">
                      ZIP Code *
                    </label>
                    <input
                      type="text"
                      value={formData.zipCode}
                      onChange={(e) => handleInputChange('zipCode', e.target.value)}
                      className="w-full p-3 border border-blue-300 rounded-lg bg-white text-blue-900 placeholder-blue-700 focus:border-blue-600 focus:ring-2 focus:ring-blue-500"
                      placeholder="99901"
                    />
                  </div>
                </div>
              </div>

              {/* Billing Address Section */}
              <div className="mt-8 p-4 bg-blue-50 rounded-lg">
                <h3 className="text-lg font-semibold text-blue-900 mb-4">Billing Address (if different from property address)</h3>

                <div className="mb-4">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.billingDifferent}
                      onChange={(e) => handleInputChange('billingDifferent', e.target.checked)}
                      className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-blue-300 rounded"
                    />
                    <span className="text-blue-900">Billing address is different from property address</span>
                  </label>
                </div>

                {formData.billingDifferent && (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-blue-900 mb-2">
                        Street Address
                      </label>
                      <input
                        type="text"
                        value={formData.billingStreetAddress}
                        onChange={(e) => handleInputChange('billingStreetAddress', e.target.value)}
                        className="w-full p-3 border border-blue-300 rounded-lg bg-white text-blue-900 placeholder-blue-700 focus:border-blue-600 focus:ring-2 focus:ring-blue-500"
                        placeholder="123 Billing Street"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-blue-900 mb-2">
                        Apartment, suite, etc. (optional)
                      </label>
                      <input
                        type="text"
                        value={formData.billingApartment}
                        onChange={(e) => handleInputChange('billingApartment', e.target.value)}
                        className="w-full p-3 border border-blue-300 rounded-lg bg-white text-blue-900 placeholder-blue-700 focus:border-blue-600 focus:ring-2 focus:ring-blue-500"
                        placeholder="Apt 2B, Suite 100, etc."
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-blue-900 mb-2">
                          City
                        </label>
                        <input
                          type="text"
                          value={formData.billingCity}
                          onChange={(e) => handleInputChange('billingCity', e.target.value)}
                          className="w-full p-3 border border-blue-300 rounded-lg bg-white text-blue-900 placeholder-blue-700 focus:border-blue-600 focus:ring-2 focus:ring-blue-500"
                          placeholder="City"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-blue-900 mb-2">
                          State
                        </label>
                        <input
                          type="text"
                          value={formData.billingState}
                          onChange={(e) => handleInputChange('billingState', e.target.value)}
                          className="w-full p-3 border border-blue-300 rounded-lg bg-white text-blue-900 placeholder-blue-700 focus:border-blue-600 focus:ring-2 focus:ring-blue-500"
                          placeholder="State"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-blue-900 mb-2">
                          ZIP Code
                        </label>
                        <input
                          type="text"
                          value={formData.billingZipCode}
                          onChange={(e) => handleInputChange('billingZipCode', e.target.value)}
                          className="w-full p-3 border border-blue-300 rounded-lg bg-white text-blue-900 placeholder-blue-700 focus:border-blue-600 focus:ring-2 focus:ring-blue-500"
                          placeholder="ZIP"
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Step 3: Property Details */}
          {currentStep === 3 && (
            <div className="space-y-6">
              <h2 className="text-xl font-bold text-blue-900 mb-4">Property Details</h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-blue-900 mb-2">
                    Property Type *
                  </label>
                  <select
                    value={formData.propertyType}
                    onChange={(e) => handleInputChange('propertyType', e.target.value)}
                    className="w-full p-3 border border-blue-300 rounded-lg bg-white text-blue-900 focus:border-blue-600 focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Select property type</option>
                    {propertyTypes.map(type => (
                      <option key={type} value={type}>{type}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-blue-900 mb-2">
                    Number of Bedrooms
                  </label>
                  <select
                    value={formData.bedrooms}
                    onChange={(e) => handleInputChange('bedrooms', e.target.value)}
                    className="w-full p-3 border border-blue-300 rounded-lg bg-white text-blue-900 focus:border-blue-600 focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Select bedrooms</option>
                    {bedroomOptions.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-blue-900 mb-2">
                    Number of Bathrooms *
                  </label>
                  <select
                    value={formData.bathrooms}
                    onChange={(e) => handleInputChange('bathrooms', e.target.value)}
                    className="w-full p-3 border border-blue-300 rounded-lg bg-white text-blue-900 focus:border-blue-600 focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Select bathrooms</option>
                    {bathroomOptions.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-blue-900 mb-2">
                    How did you hear about us? *
                  </label>
                  <select
                    value={formData.howHeard}
                    onChange={(e) => handleInputChange('howHeard', e.target.value)}
                    className="w-full p-3 border border-blue-300 rounded-lg bg-white text-blue-900 focus:border-blue-600 focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Select option</option>
                    {howHeardOptions.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Special Requirements */}
              <div className="mt-8">
                <h3 className="text-lg font-semibold text-blue-900 mb-4">Special Requirements</h3>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-blue-900 mb-2">
                      Any rooms you don't want us to clean?
                    </label>
                    <textarea
                      value={formData.roomsToSkip}
                      onChange={(e) => handleInputChange('roomsToSkip', e.target.value)}
                      rows={3}
                      className="w-full p-3 border border-blue-300 rounded-lg bg-white text-blue-900 placeholder-blue-700 focus:border-blue-600 focus:ring-2 focus:ring-blue-500"
                      placeholder="Please specify any rooms or areas to avoid..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-blue-900 mb-2">
                      Allergies or Product Sensitivities
                    </label>
                    <textarea
                      value={formData.allergies}
                      onChange={(e) => handleInputChange('allergies', e.target.value)}
                      rows={3}
                      className="w-full p-3 border border-blue-300 rounded-lg bg-white text-blue-900 placeholder-blue-700 focus:border-blue-600 focus:ring-2 focus:ring-blue-500"
                      placeholder="Please list any allergies or sensitivities to cleaning products..."
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Step 4: Service Preferences */}
          {currentStep === 4 && (
            <div className="space-y-6">
              <h2 className="text-xl font-bold text-blue-900 mb-4">Service Preferences</h2>

              <div>
                <label className="block text-sm font-medium text-blue-900 mb-2">
                  What are your main cleaning priorities? *
                </label>
                <textarea
                  value={formData.cleaningPriorities}
                  onChange={(e) => handleInputChange('cleaningPriorities', e.target.value)}
                  rows={4}
                  className="w-full p-3 border border-blue-300 rounded-lg bg-white text-blue-900 placeholder-blue-700 focus:border-blue-600 focus:ring-2 focus:ring-blue-500"
                  placeholder="Please describe your main cleaning priorities and any specific areas of focus..."
                />
              </div>

              {/* Additional Services */}
              <div className="mt-8">
                <h3 className="text-lg font-semibold text-blue-900 mb-4">Additional Services (Check all that apply)</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {additionalServiceOptions.map(service => (
                    <label key={service} className="flex items-center p-3 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                      <input
                        type="checkbox"
                        checked={formData.additionalServices.includes(service)}
                        onChange={() => handleAdditionalServiceToggle(service)}
                        className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-blue-300 rounded"
                      />
                      <span className="text-blue-900 text-sm">{service}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Step 5: Service Frequency & Budget */}
          {currentStep === 5 && (
            <div className="space-y-6">
              <h2 className="text-xl font-bold text-blue-900 mb-4">Service Frequency & Budget</h2>

              {/* Service Frequency */}
              <div>
                <h3 className="text-lg font-semibold text-blue-900 mb-4">Service Frequency *</h3>
                <div className="space-y-3">
                  {frequencyOptions.map(option => (
                    <label key={option} className="flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors cursor-pointer">
                      <input
                        type="radio"
                        name="frequency"
                        value={option}
                        checked={formData.frequency === option}
                        onChange={(e) => handleInputChange('frequency', e.target.value)}
                        className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-blue-300"
                      />
                      <span className="text-blue-900 font-medium">{option}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Budget Considerations */}
              <div className="mt-8">
                <h3 className="text-lg font-semibold text-blue-900 mb-4">Budget Considerations</h3>
                <div>
                  <label className="block text-sm font-medium text-blue-900 mb-2">
                    Budget range or constraints
                  </label>
                  <textarea
                    value={formData.budget}
                    onChange={(e) => handleInputChange('budget', e.target.value)}
                    rows={3}
                    className="w-full p-3 border border-blue-300 rounded-lg bg-white text-blue-900 placeholder-blue-700 focus:border-blue-600 focus:ring-2 focus:ring-blue-500"
                    placeholder="Please share any budget considerations or constraints..."
                  />
                </div>
              </div>
            </div>
          )}

          {/* Step 6: Agreement & Final Review */}
          {currentStep === 6 && (
            <div className="space-y-6">
              <h2 className="text-xl font-bold text-blue-900 mb-4">Agreement & Privacy Policy</h2>

              {/* Agreement Text */}
              <div className="p-6 bg-blue-50 rounded-lg">
                <p className="text-blue-900 leading-relaxed">
                  By submitting this form, you agree to our service terms and privacy policy. We will contact you within 24 hours to discuss your cleaning needs and provide a free estimate. Your information is kept confidential and is never shared with third parties.
                </p>
              </div>

              {/* Consent Checkbox */}
              <div className="p-4 border-2 border-blue-300 rounded-lg">
                <label className="flex items-start">
                  <input
                    type="checkbox"
                    checked={formData.agreedToTerms}
                    onChange={(e) => handleInputChange('agreedToTerms', e.target.checked)}
                    className="mr-3 mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-blue-300 rounded"
                  />
                  <span className="text-blue-900">
                    I agree to the service terms and privacy policy, and I consent to be contacted about my cleaning service request. *
                  </span>
                </label>
              </div>

              {/* Next Steps */}
              <div className="mt-8 p-6 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg text-white">
                <h3 className="text-xl font-bold mb-2">Next Steps</h3>
                <p className="text-lg">We'll contact you within 24 hours with your free quote!</p>
              </div>

              {/* Required Fields Notice */}
              <div className="text-center">
                <p className="text-sm text-blue-700">
                  Items marked with * are required fields
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-between items-center px-6 py-4 border-t border-blue-100">
          <button
            onClick={prevStep}
            disabled={currentStep === 1 || isSubmitting}
            className="px-6 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          
          <span className="text-blue-900 font-medium">
            Step {currentStep} of 6
          </span>
          
          <button
            onClick={nextStep}
            disabled={!canProceed() || isSubmitting}
            className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {currentStep === 6 ? 'Get My Free Quote' : 'Next'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CleaningServiceRequest;
